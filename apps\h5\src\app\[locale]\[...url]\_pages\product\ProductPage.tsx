'use client'
import { useEffect, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { ProductDetailsData, StoreListItem, TBaseComponentProps } from '@ninebot/core'
import { useVolcAnalytics } from '@ninebot/core/src/businessHooks'
import { TRACK_EVENT } from '@ninebot/core/src/constants'
import type { GetProductsQuery } from '@ninebot/core/src/graphql/generated/graphql'

import { StoreSelectorPopup } from '@/businessComponents'
import { Header, RecommendedProducts } from '@/components'
import { CustomEmpty } from '@/components/common'

// import { Share } from '@/components/icons'
import { ProductProvider, useProduct } from './context/ProductContext'
import {
  FloatingButtons,
  ProductAfterSale,
  ProductBottomBar,
  ProductDetail,
  ProductEmpty,
  ProductGallery,
  ProductInfo,
  ProductNavBar,
  ProductOptionsPopup,
  // ProductReview,
  ProductSkeleton,
  ProductSkinSound,
  ProductTermsOfSale,
} from './components'

type Products = NonNullable<GetProductsQuery['products']>
type ProductItems = NonNullable<Products['items']>
// 产品类型
type ProductItem = NonNullable<ProductItems[number]>

export type TProductPageProps = TBaseComponentProps<{
  data: ProductItem
}>

const ProductContent = ({ productDetails }: { productDetails: ProductDetailsData | null }) => {
  const {
    visibleAddCartPop,
    setVisibleAddCartPop,
    showProductRecommend,
    termsOfSale,
    doorVisible,
    setDoorVisible,
    selectStore,
    setSelectStore,
    productStatus,
    deliveryMethodPickup,
  } = useProduct() as {
    visibleAddCartPop: boolean
    setVisibleAddCartPop: (visible: boolean) => void
    showProductRecommend: boolean
    termsOfSale: {
      value: string
    }
    doorVisible: boolean
    setDoorVisible: (visible: boolean) => void
    selectStore: StoreListItem | null
    setSelectStore: (store: StoreListItem | null) => void
    productStatus: {
      id: string
    }
    deliveryMethodPickup: boolean
  }

  const productRef = useRef<HTMLDivElement>(null)
  const detailRef = useRef<HTMLDivElement>(null)
  const reviewRef = useRef<HTMLDivElement>(null)
  const recommendRef = useRef<HTMLDivElement>(null)
  const getI18nString = useTranslations('Common')
  const [soundPopVisible, setSoundPopVisible] = useState(false)

  // 添加loading状态管理
  const [isLoading, setIsLoading] = useState(true)

  // 在组件挂载后短暂延迟设置loading为false，确保骨架屏显示
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 100) // 100ms延迟，确保骨架屏能够显示

    return () => clearTimeout(timer)
  }, [])

  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return <ProductSkeleton />
  }

  return (
    <div
      className={`relative min-h-screen ${!!productDetails ? 'bg-[#F3F3F4]' : 'bg-white'} pb-[75px]`}>
      <Header />
      <ProductNavBar
        isProduct={!!productDetails}
        title="商品详情"
        // right={<Share />}
        productRef={productRef}
        detailRef={detailRef}
        reviewRef={reviewRef}
        recommendRef={recommendRef}
        hasRecommend={showProductRecommend}
        hasReview={!!productDetails?.customer_reviews?.items?.length}
        hasDetail={
          !!(productDetails?.mb_description || productDetails?.mb_after_sales_instructions)
        }
      />

      {productDetails ? (
        <>
          {/* 商品区域 */}
          <div id="product-section" ref={productRef}>
            <ProductGallery setSoundPopVisible={setSoundPopVisible} />
            <ProductInfo />
          </div>
          <div className="px-base-12">
            {/* 评价区域 */}
            {/* {productDetails?.customer_reviews?.items &&
              productDetails.customer_reviews.items.length > 0 && (
                <div id="review-section" ref={reviewRef}>
                  <ProductReview reviewData={productDetails?.customer_reviews?.items} />
                </div>
              )} */}

            {/* 详情区域 */}
            <div id="detail-section" ref={detailRef}>
              {productDetails?.mb_description ? (
                <ProductDetail content={productDetails.mb_description} />
              ) : (
                <div className="my-base flex flex-col gap-base-16 rounded-base-12 bg-white px-base-12 py-base-16 text-base">
                  <CustomEmpty description={getI18nString('empty_description')} />
                </div>
              )}

              {/* 售后说明 */}
              {productDetails?.mb_after_sales_instructions && (
                <ProductAfterSale content={productDetails.mb_after_sales_instructions} />
              )}

              {/* 条款说明 */}
              {!!termsOfSale?.value && <ProductTermsOfSale identifier={termsOfSale} />}
            </div>
          </div>

          {/* 底部操作栏 */}
          <ProductBottomBar />

          {/* 悬浮按钮 */}
          <FloatingButtons />

          {/* 规格选择弹窗 */}
          <ProductOptionsPopup
            visible={visibleAddCartPop}
            onClose={() => setVisibleAddCartPop(false)}
          />

          {/* 门店选择弹窗 */}
          {deliveryMethodPickup && (
            <StoreSelectorPopup
              doorVisible={doorVisible}
              setDoorVisible={setDoorVisible}
              selectStore={selectStore}
              setSelectStore={setSelectStore}
              setVisibleAddCartPop={setVisibleAddCartPop}
              productId={productStatus.id}
            />
          )}

          <ProductSkinSound
            sku={productDetails?.sku || ''}
            visible={soundPopVisible}
            setVisible={setSoundPopVisible}
          />
        </>
      ) : (
        <ProductEmpty />
      )}

      {/* 推荐商品 */}
      {showProductRecommend && (
        <div
          className="mt-base-12 bg-white px-base-16 pb-8 pt-[1px]"
          id="recommend-section"
          ref={recommendRef}>
          <RecommendedProducts sku={productDetails?.sku || ''} />
        </div>
      )}
    </div>
  )
}

/**
 * 产品详情页面
 * 该页面需要SSR渲染，只有需要客户操作的区域使用CSR渲染
 */
const ProductPage = ({ data }: TProductPageProps) => {
  // const { __typename, sku, name } = data

  const { reportEvent } = useVolcAnalytics()

  /**
   * 埋点：点击商品详情页
   */
  useEffect(() => {
    if (data) {
      reportEvent(TRACK_EVENT.shop_commodity_details_exposure, {
        product_id: data?.uid,
        sku_id: data?.sku,
        product_name: data?.name,
      })
    }
  }, [data, reportEvent])

  return (
    // <div>
    //   <h1>ProductPage</h1>
    //   <p>当前产品 type: {__typename}</p>
    //   <p>当前产品 sku: {sku}</p>
    //   <p>当前类型 name: {name}</p>
    // </div>
    <ProductProvider productDetails={data}>
      <ProductContent productDetails={data} />
    </ProductProvider>
  )
}

export default ProductPage
